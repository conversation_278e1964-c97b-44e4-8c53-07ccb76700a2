# Add New Prompt

You'll need to add your prompt into README.md, and to the `prompts.csv` file. If
your prompt includes quotes, you will need to double-quote them to escape in CSV
file.

If the prompt is generated by AI, please add `<mark>Generated by AI</mark>` to
the end of the contribution line.

- [ ] I've confirmed the prompt works well
- [ ] I've added
      `Contributed by: [@yourusername](https://github.com/yourusername)`
- [ ] I've added to the README.md
- [ ] I've added to the `prompts.csv`
  - [ ] Escaped quotes by double-quoting them
  - [ ] No spaces after commas after double quotes. e.g. `"Hello","hi"`, not
        `"Hello", "hi"`
  - [ ] Removed "Act as" from the title on CSV

Please make sure you've completed all the checklist.
